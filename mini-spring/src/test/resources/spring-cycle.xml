<?xml version="1.0" encoding="UTF-8" ?>
<beans>

    <bean id="husband" class="com.clint.mini.spring.framework.autowired.Husband">
        <property name="wife" ref="wife"/>
<!--         <property name="marriageDate" value="2022-01-01"/> -->
    </bean>

    <bean id="wife" class="com.clint.mini.spring.framework.autowired.Wife">
        <property name="husband" ref="husband"/>
        <property name="mother" ref="husbandMother"/>
    </bean>

    <bean id="husbandMother" class="com.clint.mini.spring.framework.autowired.HusbandMother"/>

    <!-- AOP 配置，验证三级缓存 -->
    <bean class="com.clint.mini.spring.framework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator"/>

    <bean id="beforeAdvice" class="com.clint.mini.spring.framework.autowired.SpouseAdvice"/>

    <bean id="methodInterceptor" class="com.clint.mini.spring.framework.aop.framework.adapter.MethodBeforeAdviceInterceptor">
        <property name="advice" ref="beforeAdvice"/>
    </bean>

    <bean id="pointcutAdvisor" class="com.clint.mini.spring.framework.aop.aspectj.AspectJExpressionPointcutAdvisor">
        <property name="expression" value="execution(* com.clint.mini.spring.framework.autowired.Wife.*(..))"/>
        <property name="advice" ref="methodInterceptor"/>
    </bean>

<!--     <bean id="conversionService" class="com.clint.mini.spring.framework.context.support.ConversionServiceFactoryBean"> -->
<!--         <property name="converters" ref="converters"/> -->
<!--     </bean> -->

<!--     <bean id="converters" class="com.clint.mini.spring.framework.autowired.converter.ConverterFactoryBean"/> -->

</beans>